"use client";
import { useState, useEffect } from 'react';
import { db, auth } from '../../firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { fetchUserClients, getEffectiveUserId, getUserData } from '../../utils/accountUtils';
import SideBar from '../DashBoard/SideBar';

interface BankCode {
  bankName: string;
  bankCode: string;
}

interface BankCodes {
  Credit: BankCode[];
  Debit: BankCode[];
}

interface CompteGeneral {
  code: string;
  owner: string;
}

interface BankCompteGeneral {
  bankName: string;
  compteGeneral: string;
}

interface SageCodes {
  achatCode: string;
  venteCode: string;
  caisseCode: string;
  odCode: string;
  achatsImportesCode?: string;
  bankCodes: BankCodes;
  isInternational?: boolean;
  comptesGeneralesBank?: {
    fraisBancaire: string;
    tvaDeductible: string;
    compteAttente: string;
    compteClient: string;
  };
  bankComptesGeneral?: BankCompteGeneral[];
  salaryCodes?: {
    banqueJournalCode: string;
    salaireJournalCode: string;
  };
}

interface Client {
  name: string;
  employeesNumber: string;
  sageCodes: SageCodes;
  comptesGeneral: Record<string, CompteGeneral>;
  achat: Record<string, { code: string }>;
  vente: Record<string, { code: string }>;
  salary: Record<string, { code: string }>;
}

interface Clients {
  [key: string]: Client;
}

export default function ClientList() {
  const [clients, setClients] = useState<Clients>({});
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingClient, setEditingClient] = useState<{ id: string; data: Client } | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<string | null>(null);
  const router = useRouter();
  const [visibleComptesCount, setVisibleComptesCount] = useState<Record<string, number>>({});
  const [isSubAccount, setIsSubAccount] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push('/'); // Redirect to login if not authenticated
        return;
      }

      try {
        // Check if the user is a sub-account
        const userData = await getUserData(user.uid);
        setIsSubAccount(!!userData?.isSubAccount);

        // Use the utility function to fetch clients (handles both regular users and sub-accounts)
        const clientsData = await fetchUserClients(user);
        setClients(clientsData);
      } catch (error) {
        console.error("Error fetching clients:", error);
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [router]);

  const handleAddClient = async (newClient: Client) => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      const newClients = { ...clients, [`client_${Date.now()}`]: newClient };
      await updateDoc(doc(db, 'users', effectiveUserId), { clients: newClients });
      setClients(newClients);
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error adding client:", error);
    }
  };

  const handleEditClient = async (clientId: string, updatedClient: Client) => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      const newClients = { ...clients, [clientId]: updatedClient };
      await updateDoc(doc(db, 'users', effectiveUserId), { clients: newClients });
      setClients(newClients);
      setEditingClient(null);
    } catch (error) {
      console.error("Error updating client:", error);
    }
  };

  const handleDeleteClient = async (clientId: string) => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      // Prevent sub-accounts from deleting clients
      if (isSubAccount) {
        alert("Sub-accounts are not allowed to delete clients. Please contact your administrator.");
        return;
      }

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      const newClients = { ...clients };
      delete newClients[clientId];
      await updateDoc(doc(db, 'users', effectiveUserId), { clients: newClients });
      setClients(newClients);
      setIsDeleteModalOpen(false);
      setClientToDelete(null);
    } catch (error) {
      console.error("Error deleting client:", error);
    }
  };

  const handleViewMore = (clientId: string) => {
    setVisibleComptesCount(prev => ({
      ...prev,
      [clientId]: (prev[clientId] || 8) + 8
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-100">
      <SideBar />
      <div className="flex-1">
        <div className="p-8 max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800">Client List</h1>
            <button
              onClick={() => setIsModalOpen(true)}
              className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition duration-200 flex items-center space-x-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              <span>Add New Client</span>
            </button>
          </div>

          <div className="grid gap-6">
            {Object.entries(clients).map(([clientId, client]) => (
              <div key={clientId} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition duration-200">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center space-x-4">
                      <h2 className="text-xl font-semibold text-gray-800">{client.name}</h2>
                    </div>
                    <div className="space-x-3">
                      <button
                        onClick={() => setEditingClient({ id: clientId, data: client })}
                        className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                        Edit Details
                      </button>
                      {!isSubAccount && (
                        <button
                          onClick={() => {
                            setClientToDelete(clientId);
                            setIsDeleteModalOpen(true);
                          }}
                          className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-200"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          Delete
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    {/* Sage Codes Section - Simplified */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2 mb-4">Sage Codes</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100 shadow-sm">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Achat</p>
                          <p className="text-lg font-bold text-indigo-700">{client.sageCodes.achatCode || 'N/A'}</p>
                        </div>
                        <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg border border-green-100 shadow-sm">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Vente</p>
                          <p className="text-lg font-bold text-emerald-700">{client.sageCodes.venteCode || 'N/A'}</p>
                        </div>
                        <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-100 shadow-sm">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Caisse</p>
                          <p className="text-lg font-bold text-violet-700">{client.sageCodes.caisseCode || 'N/A'}</p>
                        </div>
                        <div className="bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-100 shadow-sm">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">OD</p>
                          <p className="text-lg font-bold text-amber-700">{client.sageCodes.odCode || 'N/A'}</p>
                        </div>
                        {client.sageCodes.isInternational && client.sageCodes.achatsImportesCode && (
                          <div className="bg-gradient-to-br from-teal-50 to-cyan-50 p-4 rounded-lg border border-teal-100 shadow-sm col-span-2">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Achats Importés</p>
                            <p className="text-lg font-bold text-teal-700">{client.sageCodes.achatsImportesCode}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Comptes General Section - Simplified */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2 mb-4">Comptes Général</h3>
                      <div className="space-y-3 max-h-80 overflow-y-auto">
                        {Object.entries(client.comptesGeneral)
                          .slice(0, visibleComptesCount[clientId] || 8)
                          .map(([code, compte]) => (
                            <div key={code} className="bg-gradient-to-r from-gray-50 to-slate-50 p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                              <div className="flex justify-between items-center">
                                <div>
                                  <p className="text-sm font-medium text-gray-600">{compte.owner}</p>
                                  <p className="text-lg font-bold text-slate-700">{compte.code}</p>
                                </div>
                                <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
                              </div>
                            </div>
                          ))}
                      </div>
                      {Object.keys(client.comptesGeneral).length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <p className="text-sm italic">No comptes général configured</p>
                        </div>
                      )}
                      {Object.keys(client.comptesGeneral).length > (visibleComptesCount[clientId] || 8) && (
                        <button
                          onClick={() => handleViewMore(clientId)}
                          className="mt-4 w-full py-3 px-4 bg-gradient-to-r from-gray-50 to-slate-50 text-gray-700 rounded-lg hover:from-gray-100 hover:to-slate-100 transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 shadow-sm hover:shadow-md"
                        >
                          <span className="font-medium">View {Object.keys(client.comptesGeneral).length - (visibleComptesCount[clientId] || 8)} More</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add/Edit Modal */}
          {(isModalOpen || editingClient) && (
            <ClientModal
              isOpen={true}
              onClose={() => {
                setIsModalOpen(false);
                setEditingClient(null);
              }}
              onSubmit={(client) => {
                if (editingClient) {
                  handleEditClient(editingClient.id, client);
                } else {
                  handleAddClient(client);
                }
              }}
              initialData={editingClient?.data}
            />
          )}

          {/* Delete Confirmation Modal */}
          {isDeleteModalOpen && clientToDelete && (
            <DeleteConfirmationModal
              isOpen={true}
              onClose={() => {
                setIsDeleteModalOpen(false);
                setClientToDelete(null);
              }}
              onConfirm={() => handleDeleteClient(clientToDelete)}
              clientName={clients[clientToDelete].name}
            />
          )}
        </div>
      </div>
    </div>
  );
}

// Add these components at the end of the file
function ClientModal({ isOpen, onClose, onSubmit, initialData }: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (client: Client) => void;
  initialData?: Client;
}) {
  const [formData, setFormData] = useState<Client>(initialData || {
    name: '',
    employeesNumber: '',
    sageCodes: {
      achatCode: '',
      venteCode: '',
      caisseCode: '',
      odCode: '',
      achatsImportesCode: '',
      bankCodes: {
        Credit: [],
        Debit: []
      },
      isInternational: false,
      comptesGeneralesBank: {
        fraisBancaire: '',
        tvaDeductible: '',
        compteAttente: '',
        compteClient: ''
      },
      bankComptesGeneral: [],
      salaryCodes: {
        banqueJournalCode: '',
        salaireJournalCode: ''
      }
    },
    comptesGeneral: {},
    achat: {
      "Montant HTVA": { code: "" },
      "TVA": { code: "" },
      "TIMBRE FISCAL": { code: "" }
    },
    vente: {
      "Montant HTVA": { code: "" },
      "TVA": { code: "" },
      "TIMBRE FISCAL": { code: "" },
      "COMPTE CLIENT": { code: "" }
    },
    salary: {
      "SALAIRE ET COMPLEMENT DE SALAIRE": { code: "" },
      "CHARGE SOCIALE LEGALE": { code: "" },
      "TFP": { code: "" },
      "FOPROLOS": { code: "" },
      "RETENUE A LA SOURCE SUR SALAIRE": { code: "" },
      "PERSONNEL AVANCE ET ACCOMPTE": { code: "" },
      "CNSS": { code: "" },
      "Bank Compte General": { code: "" },
      "AUTRES IMPOT TAXES": { code: "" }
    }
  });

  const [showComptesModal, setShowComptesModal] = useState(false);
  const visibleComptesLimit = 5;

  const [newBankCode, setNewBankCode] = useState({
    bankName: '',
    bankCode: '',
    type: 'Credit' // Default type
  });
  const [newCompteGeneral, setNewCompteGeneral] = useState({ code: '', owner: '' });
  const [newBankCompteGeneral, setNewBankCompteGeneral] = useState({
    bankName: '',
    compteGeneral: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const addBankCode = () => {
    if (newBankCode.bankName && newBankCode.bankCode) {
      const bankCodeData = {
        bankName: newBankCode.bankName,
        bankCode: newBankCode.bankCode
      };

      setFormData({
        ...formData,
        sageCodes: {
          ...formData.sageCodes,
          bankCodes: {
            ...formData.sageCodes.bankCodes,
            [newBankCode.type]: [
              ...formData.sageCodes.bankCodes[newBankCode.type as 'Credit' | 'Debit'],
              bankCodeData
            ]
          }
        }
      });
      setNewBankCode({ bankName: '', bankCode: '', type: 'Credit' });
    }
  };

  const removeBankCode = (index: number, type: 'Credit' | 'Debit') => {
    setFormData({
      ...formData,
      sageCodes: {
        ...formData.sageCodes,
        bankCodes: {
          ...formData.sageCodes.bankCodes,
          [type]: formData.sageCodes.bankCodes[type].filter((_, i) => i !== index)
        }
      }
    });
  };

  const addCompteGeneral = () => {
    if (newCompteGeneral.code && newCompteGeneral.owner) {
      // Check if code already exists
      if (formData.comptesGeneral[newCompteGeneral.code]) {
        alert('This compte general code already exists!');
        return;
      }

      setFormData({
        ...formData,
        comptesGeneral: {
          ...formData.comptesGeneral,
          [newCompteGeneral.code]: {
            code: newCompteGeneral.code,
            owner: newCompteGeneral.owner
          }
        }
      });
      setNewCompteGeneral({ code: '', owner: '' });
    }
  };

  const removeCompteGeneral = (code: string) => {
    const newComptesGeneral = { ...formData.comptesGeneral };
    delete newComptesGeneral[code];
    setFormData({ ...formData, comptesGeneral: newComptesGeneral });
  };

  const addBankCompteGeneral = () => {
    if (newBankCompteGeneral.bankName && newBankCompteGeneral.compteGeneral) {
      setFormData({
        ...formData,
        sageCodes: {
          ...formData.sageCodes,
          bankComptesGeneral: [
            ...(formData.sageCodes.bankComptesGeneral || []),
            { ...newBankCompteGeneral }
          ]
        }
      });
      setNewBankCompteGeneral({ bankName: '', compteGeneral: '' });
    }
  };

  const removeBankCompteGeneral = (index: number) => {
    setFormData({
      ...formData,
      sageCodes: {
        ...formData.sageCodes,
        bankComptesGeneral: (formData.sageCodes.bankComptesGeneral || []).filter((_, i) => i !== index)
      }
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const text = event.target?.result as string;
      const lines = text.split('\n');
      const newComptesGeneral: Record<string, CompteGeneral> = {};
      const duplicates: string[] = [];

      lines.forEach(line => {
        if (!line.trim()) return;

        const match = line.match(/^(.+?)\s+(\d+)\s*$/);
        if (match) {
          const [, owner, code] = match;
          // Check if code already exists in current form data
          if (formData.comptesGeneral[code.trim()]) {
            duplicates.push(code.trim());
          } else {
            newComptesGeneral[code.trim()] = {
              code: code.trim(),
              owner: owner.trim()
            };
          }
        }
      });

      // Show warning if there were duplicates
      if (duplicates.length > 0) {
        alert(`The following codes were skipped because they already exist:\n${duplicates.join(', ')}`);
      }

      setFormData({
        ...formData,
        comptesGeneral: {
          ...formData.comptesGeneral,
          ...newComptesGeneral
        }
      });
    };
    reader.readAsText(file);
  };

  const updateAchatCode = (field: string, code: string) => {
    setFormData(prev => ({
      ...prev,
      achat: {
        ...prev.achat,
        [field]: { code }
      }
    }));
  };

  const updateVenteCode = (field: string, code: string) => {
    setFormData(prev => ({
      ...prev,
      vente: {
        ...prev.vente,
        [field]: { code }
      }
    }));
  };

  const updateComptesGeneralesBank = (field: string, value: string) => {
    const currentBank = formData.sageCodes.comptesGeneralesBank || {
      fraisBancaire: '',
      tvaDeductible: '',
      compteAttente: '',
      compteClient: ''
    };

    setFormData({
      ...formData,
      sageCodes: {
        ...formData.sageCodes,
        comptesGeneralesBank: {
          ...currentBank,
          [field]: value
        }
      }
    });
  };

  const updateSalaryCode = (field: 'banqueJournalCode' | 'salaireJournalCode', value: string) => {
    const currentSalaryCodes = formData.sageCodes.salaryCodes || {
      banqueJournalCode: '',
      salaireJournalCode: ''
    };

    setFormData({
      ...formData,
      sageCodes: {
        ...formData.sageCodes,
        salaryCodes: {
          ...currentSalaryCodes,
          [field]: value
        }
      }
    });
  };

  const updateSalaryCompteGeneral = (field: string, code: string) => {
    setFormData(prev => ({
      ...prev,
      salary: {
        ...prev.salary,
        [field]: { code }
      }
    }));
  };

  useEffect(() => {
    const updatedAchat = {...formData.achat};
    const updatedVente = {...formData.vente};

    if (formData.sageCodes.isInternational) {
      // Add international fields if they don't exist
      if (!updatedAchat["ACHAT IMPORTES"]) {
        updatedAchat["ACHAT IMPORTES"] = { code: "" };
      }
      if (!updatedVente["VENTE EXONERE"]) {
        updatedVente["VENTE EXONERE"] = { code: "" };
      }
    } else {
      // Remove international fields if they exist
      if (updatedAchat["ACHAT IMPORTES"]) {
        delete updatedAchat["ACHAT IMPORTES"];
      }
      if (updatedVente["VENTE EXONERE"]) {
        delete updatedVente["VENTE EXONERE"];
      }
    }

    // Only update if there are changes
    if (JSON.stringify(updatedAchat) !== JSON.stringify(formData.achat) ||
        JSON.stringify(updatedVente) !== JSON.stringify(formData.vente)) {
      setFormData(prev => ({
        ...prev,
        achat: updatedAchat,
        vente: updatedVente
      }));
    }
  }, [formData.sageCodes.isInternational]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-start justify-center overflow-y-auto z-50">
      <div className="bg-white w-full min-h-screen relative">
        {/* Fixed Header */}
        <header className="sticky top-0 z-10 bg-white shadow-md px-6 py-4 flex justify-between items-center border-b">
          <h2 className="text-2xl font-bold text-gray-800">
            {initialData ? 'Edit Client' : 'Add New Client'}
          </h2>
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium"
            >
              {initialData ? 'Update' : 'Create'} Client
            </button>
          </div>
        </header>

        {/* Main Content */}
        <div className="p-6 max-w-7xl mx-auto">
          <form className="space-y-8">
            {/* Client Name */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <label className="block text-lg font-medium text-gray-800 mb-4">
                Client Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="block w-full rounded-lg border border-gray-300 px-4 py-3 text-lg"
                required
                placeholder="Enter client name"
              />
            </div>

            {/* Employees Number */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <label className="block text-lg font-medium text-gray-800 mb-4">
                Employees Number
              </label>
              <input
                type="text"
                value={formData.employeesNumber}
                onChange={(e) => setFormData({ ...formData, employeesNumber: e.target.value })}
                className="block w-full rounded-lg border border-gray-300 px-4 py-3 text-lg"
                placeholder="Enter number of employees"
              />
            </div>

            {/* Sage Codes */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-4">Sage Codes</h3>

              {/* International Company Checkbox */}
              <div className="mb-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.sageCodes.isInternational || false}
                    onChange={(e) => setFormData({
                      ...formData,
                      sageCodes: {
                        ...formData.sageCodes,
                        isInternational: e.target.checked
                      }
                    })}
                    className="form-checkbox h-5 w-5 text-indigo-600 rounded"
                  />
                  <span className="ml-2 text-gray-700 font-medium">International Company</span>
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Achat Code
                  </label>
                  <input
                    type="text"
                    value={formData.sageCodes.achatCode}
                    onChange={(e) => setFormData({
                      ...formData,
                      sageCodes: { ...formData.sageCodes, achatCode: e.target.value }
                    })}
                    className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                    placeholder="Achat code"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vente Code
                  </label>
                  <input
                    type="text"
                    value={formData.sageCodes.venteCode}
                    onChange={(e) => setFormData({
                      ...formData,
                      sageCodes: { ...formData.sageCodes, venteCode: e.target.value }
                    })}
                    className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                    placeholder="Vente code"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Caisse Code
                  </label>
                  <input
                    type="text"
                    value={formData.sageCodes.caisseCode}
                    onChange={(e) => setFormData({
                      ...formData,
                      sageCodes: { ...formData.sageCodes, caisseCode: e.target.value }
                    })}
                    className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                    placeholder="Caisse code"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    OD Code
                  </label>
                  <input
                    type="text"
                    value={formData.sageCodes.odCode}
                    onChange={(e) => setFormData({
                      ...formData,
                      sageCodes: { ...formData.sageCodes, odCode: e.target.value }
                    })}
                    className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                    placeholder="OD code"
                  />
                </div>
                {formData.sageCodes.isInternational && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Achats importes Code
                    </label>
                    <input
                      type="text"
                      value={formData.sageCodes.achatsImportesCode || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        sageCodes: { ...formData.sageCodes, achatsImportesCode: e.target.value }
                      })}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder="Achats importes code"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Bank Codes */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-6">Bank Codes</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Recette Bank Codes */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h4 className="text-lg font-medium text-gray-700 mb-4">Credit</h4>
                  <div className="space-y-3 max-h-60 overflow-y-auto mb-4">
                    {formData.sageCodes.bankCodes.Credit.map((bank, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <div className="font-medium">
                          <span className="text-indigo-600">{bank.bankName}</span>
                          <span className="mx-2 text-gray-400">|</span>
                          <span>{bank.bankCode}</span>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeBankCode(index, 'Credit')}
                          className="text-red-500 hover:text-red-700 p-1"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    ))}
                    {formData.sageCodes.bankCodes.Credit.length === 0 && (
                      <div className="text-gray-500 italic text-center py-4">
                        No Credit bank codes added yet
                      </div>
                    )}
                  </div>
                </div>

                {/* Debit Bank Codes */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h4 className="text-lg font-medium text-gray-700 mb-4">Debit</h4>
                  <div className="space-y-3 max-h-60 overflow-y-auto mb-4">
                    {formData.sageCodes.bankCodes.Debit.map((bank, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <div className="font-medium">
                          <span className="text-indigo-600">{bank.bankName}</span>
                          <span className="mx-2 text-gray-400">|</span>
                          <span>{bank.bankCode}</span>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeBankCode(index, 'Debit')}
                          className="text-red-500 hover:text-red-700 p-1"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    ))}
                    {formData.sageCodes.bankCodes.Debit.length === 0 && (
                      <div className="text-gray-500 italic text-center py-4">
                        No Debit bank codes added yet
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Add a form for adding Credit/Debit bank codes */}
              <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200">
                <h4 className="text-md font-medium text-gray-700 mb-3">Add Bank Code</h4>
                <div className="flex flex-col md:flex-row gap-3">
                  <input
                    type="text"
                    placeholder="Bank Name"
                    value={newBankCode.bankName}
                    onChange={(e) => setNewBankCode({ ...newBankCode, bankName: e.target.value })}
                    className="flex-1 rounded-lg border border-gray-300 px-3 py-2"
                  />
                  <input
                    type="text"
                    placeholder="Bank Code"
                    value={newBankCode.bankCode}
                    onChange={(e) => setNewBankCode({ ...newBankCode, bankCode: e.target.value })}
                    className="flex-1 rounded-lg border border-gray-300 px-3 py-2"
                  />
                  <select
                    value={newBankCode.type}
                    onChange={(e) => setNewBankCode({ ...newBankCode, type: e.target.value })}
                    className="flex-1 rounded-lg border border-gray-300 px-3 py-2"
                  >
                    <option value="Credit">Credit</option>
                    <option value="Debit">Debit</option>
                  </select>
                  <button
                    type="button"
                    onClick={addBankCode}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                  >
                    Add
                  </button>
                </div>
              </div>

              {/* Bank Comptes General Section */}
              <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200">
                <h4 className="text-md font-medium text-gray-700 mb-3">Bank Comptes General</h4>

                <div className="space-y-3 max-h-60 overflow-y-auto mb-4">
                  {(formData.sageCodes.bankComptesGeneral || []).map((item, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                      <div className="font-medium">
                        <span className="text-indigo-600">{item.bankName}</span>
                        <span className="mx-2 text-gray-400">|</span>
                        <span>{item.compteGeneral}</span>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeBankCompteGeneral(index)}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  ))}
                  {(!formData.sageCodes.bankComptesGeneral || formData.sageCodes.bankComptesGeneral.length === 0) && (
                    <div className="text-gray-500 italic text-center py-4">
                      No Bank Comptes General added yet
                    </div>
                  )}
                </div>

                <div className="flex flex-col md:flex-row gap-3">
                  <input
                    type="text"
                    placeholder="Bank Name"
                    value={newBankCompteGeneral.bankName}
                    onChange={(e) => setNewBankCompteGeneral({ ...newBankCompteGeneral, bankName: e.target.value })}
                    className="flex-1 rounded-lg border border-gray-300 px-3 py-2"
                  />
                  <input
                    type="text"
                    placeholder="Compte General"
                    value={newBankCompteGeneral.compteGeneral}
                    onChange={(e) => setNewBankCompteGeneral({ ...newBankCompteGeneral, compteGeneral: e.target.value })}
                    className="flex-1 rounded-lg border border-gray-300 px-3 py-2"
                  />
                  <button
                    type="button"
                    onClick={addBankCompteGeneral}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 whitespace-nowrap"
                  >
                    Add
                  </button>
                </div>
              </div>

              {/* Comptes Generales Bank Section */}
              <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200">
                <h4 className="text-md font-medium text-gray-700 mb-3">Comptes Generales Bank</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      FRAIS BANCAIRE
                    </label>
                    <input
                      type="text"
                      value={formData.sageCodes.comptesGeneralesBank?.fraisBancaire || ''}
                      onChange={(e) => updateComptesGeneralesBank('fraisBancaire', e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder="Enter FRAIS BANCAIRE code"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      TVA DÉDUCTIBLE
                    </label>
                    <input
                      type="text"
                      value={formData.sageCodes.comptesGeneralesBank?.tvaDeductible || ''}
                      onChange={(e) => updateComptesGeneralesBank('tvaDeductible', e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder="Enter TVA DÉDUCTIBLE code"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      compte d'attente
                    </label>
                    <input
                      type="text"
                      value={formData.sageCodes.comptesGeneralesBank?.compteAttente || ''}
                      onChange={(e) => updateComptesGeneralesBank('compteAttente', e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder="Enter compte d'attente code"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Compte Client
                    </label>
                    <input
                      type="text"
                      value={formData.sageCodes.comptesGeneralesBank?.compteClient || ''}
                      onChange={(e) => updateComptesGeneralesBank('compteClient', e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder="Enter Compte Client code"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Comptes General */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-medium text-gray-800">Comptes General</h3>
                <button
                  type="button"
                  onClick={() => setShowComptesModal(true)}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm"
                >
                  Edit All Comptes
                </button>
              </div>

              {/* Preview of comptes */}
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-80 overflow-y-auto">
                  {Object.entries(formData.comptesGeneral)
                    .slice(0, visibleComptesLimit)
                    .map(([code, compte]) => (
                    <div key={code} className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                      <p className="font-medium text-gray-800">{compte.code}</p>
                      <p className="text-sm text-gray-600">{compte.owner}</p>
                    </div>
                  ))}
                </div>
                {Object.keys(formData.comptesGeneral).length === 0 && (
                  <div className="text-gray-500 italic text-center py-8">
                    No comptes general added yet
                  </div>
                )}
                {Object.keys(formData.comptesGeneral).length > visibleComptesLimit && (
                  <div className="mt-4 text-center text-sm text-gray-500">
                    + {Object.keys(formData.comptesGeneral).length - visibleComptesLimit} more comptes...
                    <button
                      type="button"
                      onClick={() => setShowComptesModal(true)}
                      className="ml-2 text-indigo-600 hover:text-indigo-800"
                    >
                      View all
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Achat Codes Section */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-6">Achat Codes</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(formData.achat).map(([field, value]) => (
                  <div key={field} className="bg-white p-4 rounded-lg border border-gray-200">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {field}
                    </label>
                    <input
                      type="text"
                      value={value.code}
                      onChange={(e) => updateAchatCode(field, e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder={`Enter ${field} code`}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Vente Codes Section */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-6">Vente Codes</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(formData.vente).map(([field, value]) => (
                  <div key={field} className="bg-white p-4 rounded-lg border border-gray-200">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {field}
                    </label>
                    <input
                      type="text"
                      value={value.code}
                      onChange={(e) => updateVenteCode(field, e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder={`Enter ${field} code`}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Salary Codes Section */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-6">Salary Codes</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banque Journal Code
                  </label>
                  <input
                    type="text"
                    value={formData.sageCodes.salaryCodes?.banqueJournalCode || ''}
                    onChange={(e) => updateSalaryCode('banqueJournalCode', e.target.value)}
                    className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                    placeholder="Enter Banque Journal Code"
                  />
                </div>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Salaire Journal Code
                  </label>
                  <input
                    type="text"
                    value={formData.sageCodes.salaryCodes?.salaireJournalCode || ''}
                    onChange={(e) => updateSalaryCode('salaireJournalCode', e.target.value)}
                    className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                    placeholder="Enter Salaire Journal Code"
                  />
                </div>
              </div>
            </div>

            {/* Salary Comptes Generale Section */}
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-6">Salary Comptes Generale</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(formData.salary).map(([field, value]) => (
                  <div key={field} className="bg-white p-4 rounded-lg border border-gray-200">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {field}
                    </label>
                    <input
                      type="text"
                      value={value.code}
                      onChange={(e) => updateSalaryCompteGeneral(field, e.target.value)}
                      className="block w-full rounded-lg border border-gray-300 px-3 py-2"
                      placeholder={`Enter ${field} Compte General`}
                    />
                  </div>
                ))}
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Add new Comptes General Edit Modal */}
      {showComptesModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-full max-w-4xl my-8 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-medium">Edit Comptes General</h3>
              <button
                type="button"
                onClick={() => setShowComptesModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            {/* Add "Remove All" button */}
            <div className="mb-4 flex justify-end">
              <button
                type="button"
                onClick={() => {
                  if (confirm('Are you sure you want to remove all comptes general? This action cannot be undone.')) {
                    setFormData({
                      ...formData,
                      comptesGeneral: {}
                    });
                  }
                }}
                className="px-3 py-1.5 bg-red-600 text-white text-sm rounded hover:bg-red-700"
              >
                Remove All Comptes
              </button>
            </div>

            {/* File upload section */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Import Comptes General from File
              </label>
              <input
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-semibold
                  file:bg-indigo-50 file:text-indigo-700
                  hover:file:bg-indigo-100"
              />
            </div>

            {/* Add new compte form */}
            <div className="flex space-x-2 mb-4">
              <input
                type="text"
                placeholder="Code"
                value={newCompteGeneral.code}
                onChange={(e) => setNewCompteGeneral({ ...newCompteGeneral, code: e.target.value })}
                className="flex-1 rounded-md border border-gray-300 px-3 py-2"
              />
              <input
                type="text"
                placeholder="Owner"
                value={newCompteGeneral.owner}
                onChange={(e) => setNewCompteGeneral({ ...newCompteGeneral, owner: e.target.value })}
                className="flex-1 rounded-md border border-gray-300 px-3 py-2"
              />
              <button
                type="button"
                onClick={addCompteGeneral}
                className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
              >
                Add
              </button>
            </div>

            {/* List all comptes */}
            <div className="space-y-2 max-h-[50vh] overflow-y-auto">
              {Object.entries(formData.comptesGeneral).map(([code, compte]) => (
                <div key={code} className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-50 p-2 rounded">
                    {compte.owner} - {compte.code}
                  </div>
                  <button
                    type="button"
                    onClick={() => removeCompteGeneral(code)}
                    className="text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>

            <div className="mt-4 flex justify-end">
              <button
                type="button"
                onClick={() => setShowComptesModal(false)}
                className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function DeleteConfirmationModal({ isOpen, onClose, onConfirm, clientName }: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  clientName: string;
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white p-6 rounded-lg">
        <h3 className="text-lg font-medium mb-4">Delete Client</h3>
        <p>Are you sure you want to delete {clientName}?</p>
        <div className="mt-4 flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}