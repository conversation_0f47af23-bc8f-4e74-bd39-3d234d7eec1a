"use client";
import React, { useState, useEffect, useCallback } from "react";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import {
  getFirestore,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  startAfter,
  DocumentSnapshot,
  Timestamp
} from "firebase/firestore";
import {
  getStorage,
  ref,
  listAll,
  getDownloadURL,
  getMetadata
} from "firebase/storage";
import { useRouter } from "next/navigation";
import Sidebar from "../../DashBoard/SideBar";
import { fetchUserClients, getEffectiveUserId } from "../../../utils/accountUtils";
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  XCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentPlusIcon,
  BookOpenIcon,
  PlusCircleIcon
} from "@heroicons/react/24/outline";
import { Transition, Dialog } from '@headlessui/react';
import { Fragment } from 'react';

// Interfaces
interface Client {
  id: string;
  name: string;
}

interface PayslipFile {
  id: string;
  fileName: string;
  fileUrl: string;
  uploadDate: Date;
  clientId: string;
  clientName: string;
  month: number;
  year: number;
  employeeName?: string;
  status: 'processed' | 'pending' | 'failed' | 'processing';
  fileSize: number;
  fileType: string;
  processingResult?: any;
  storagePath: string;
}

interface FilterState {
  clientId: string;
  month: number;
  year: number;
  status: string;
  searchTerm: string;
}

interface DashboardStats {
  totalPayslips: number;
  processedCount: number;
  pendingCount: number;
  failedCount: number;
  monthlyTrends: { month: string; count: number }[];
}

const PayslipsDashboard: React.FC = () => {
  // State management
  const [user, setUser] = useState<any>(null);
  const [clients, setClients] = useState<Client[]>([]);
  const [payslips, setPayslips] = useState<PayslipFile[]>([]);
  const [filteredPayslips, setFilteredPayslips] = useState<PayslipFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalPayslips: 0,
    processedCount: 0,
    pendingCount: 0,
    failedCount: 0,
    monthlyTrends: []
  });

  // Filter and pagination state
  const [filters, setFilters] = useState<FilterState>({
    clientId: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    status: '',
    searchTerm: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  const [selectedPayslip, setSelectedPayslip] = useState<PayslipFile | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);

  const router = useRouter();

  // Authentication check
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(getAuth(), (user) => {
      if (user) {
        setUser(user);
      } else {
        router.push('/SignInScreen');
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Fetch clients
  useEffect(() => {
    const loadClients = async () => {
      if (user) {
        try {
          const clientsData = await fetchUserClients(user);
          // Convert Record<string, any> to Client[]
          const clientsArray: Client[] = Object.entries(clientsData).map(([id, data]) => ({
            id,
            name: data.name || 'Unknown Client'
          }));
          setClients(clientsArray);
        } catch (error) {
          console.error('Error fetching clients:', error);
        }
      }
    };

    loadClients();
  }, [user]);

  // Fetch payslips from Firebase Storage
  const fetchPayslipsFromStorage = useCallback(async () => {
    if (!user || clients.length === 0) return;

    setLoading(true);
    try {
      const storage = getStorage();
      const allPayslips: PayslipFile[] = [];

      // Iterate through each client
      for (const client of clients) {
        const clientPath = `payslips/${client.id}`;
        const clientRef = ref(storage, clientPath);

        try {
          // List all years for this client
          const yearsList = await listAll(clientRef);

          for (const yearRef of yearsList.prefixes) {
            const year = parseInt(yearRef.name);

            // List all months for this year
            const monthsList = await listAll(yearRef);

            for (const monthRef of monthsList.prefixes) {
              const month = parseInt(monthRef.name);

              // List all files in this month
              const filesList = await listAll(monthRef);

              for (const fileRef of filesList.items) {
                try {
                  const [downloadUrl, metadata] = await Promise.all([
                    getDownloadURL(fileRef),
                    getMetadata(fileRef)
                  ]);

                  // Extract employee name from filename if possible
                  const fileName = fileRef.name;
                  const employeeName = extractEmployeeNameFromFilename(fileName);

                  const payslipFile: PayslipFile = {
                    id: `${client.id}_${year}_${month}_${fileName}`,
                    fileName: fileName,
                    fileUrl: downloadUrl,
                    uploadDate: new Date(metadata.timeCreated),
                    clientId: client.id,
                    clientName: client.name,
                    month: month,
                    year: year,
                    employeeName: employeeName,
                    status: determineProcessingStatus(fileName),
                    fileSize: metadata.size,
                    fileType: metadata.contentType || 'application/octet-stream',
                    storagePath: fileRef.fullPath
                  };

                  allPayslips.push(payslipFile);
                } catch (fileError) {
                  console.error(`Error processing file ${fileRef.name}:`, fileError);
                }
              }
            }
          }
        } catch (clientError) {
          console.error(`Error processing client ${client.name}:`, clientError);
        }
      }

      // Sort by upload date (newest first)
      allPayslips.sort((a, b) => b.uploadDate.getTime() - a.uploadDate.getTime());

      setPayslips(allPayslips);
      calculateStats(allPayslips);
    } catch (error) {
      console.error('Error fetching payslips:', error);
    } finally {
      setLoading(false);
    }
  }, [user, clients]);

  useEffect(() => {
    fetchPayslipsFromStorage();
  }, [fetchPayslipsFromStorage]);

  // Helper function to extract employee name from filename
  const extractEmployeeNameFromFilename = (fileName: string): string | undefined => {
    // Remove timestamp and file extension
    const cleanName = fileName.replace(/^\d+_/, '').replace(/\.[^/.]+$/, '');

    // Try to extract employee name patterns
    const patterns = [
      /payslip[_-](.+)/i,
      /bulletin[_-](.+)/i,
      /salary[_-](.+)/i,
      /^(.+)[_-]payslip/i,
      /^(.+)[_-]bulletin/i
    ];

    for (const pattern of patterns) {
      const match = cleanName.match(pattern);
      if (match && match[1]) {
        return match[1].replace(/[_-]/g, ' ').trim();
      }
    }

    // If no pattern matches, return the clean filename
    return cleanName.replace(/[_-]/g, ' ').trim() || undefined;
  };

  // Helper function to determine processing status
  const determineProcessingStatus = (fileName: string): 'processed' | 'pending' | 'failed' | 'processing' => {
    // This is a simplified status determination
    // In a real implementation, you would check against a database or processing logs
    const lowerFileName = fileName.toLowerCase();

    if (lowerFileName.includes('processed') || lowerFileName.includes('completed')) {
      return 'processed';
    } else if (lowerFileName.includes('failed') || lowerFileName.includes('error')) {
      return 'failed';
    } else if (lowerFileName.includes('processing')) {
      return 'processing';
    }

    // Default to processed for existing files (assuming they were processed when uploaded)
    return 'processed';
  };

  // Calculate dashboard statistics
  const calculateStats = (payslipsList: PayslipFile[]) => {
    const totalPayslips = payslipsList.length;
    const processedCount = payslipsList.filter(p => p.status === 'processed').length;
    const pendingCount = payslipsList.filter(p => p.status === 'pending').length;
    const failedCount = payslipsList.filter(p => p.status === 'failed').length;

    // Calculate monthly trends for the last 12 months
    const monthlyTrends: { month: string; count: number }[] = [];
    const currentDate = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      const count = payslipsList.filter(p =>
        p.year === date.getFullYear() && p.month === date.getMonth() + 1
      ).length;

      monthlyTrends.push({ month: monthName, count });
    }

    setStats({
      totalPayslips,
      processedCount,
      pendingCount,
      failedCount,
      monthlyTrends
    });
  };

  // Filter payslips based on current filters
  useEffect(() => {
    let filtered = payslips;

    // Filter by client
    if (filters.clientId) {
      filtered = filtered.filter(p => p.clientId === filters.clientId);
    }

    // Filter by month/year
    if (filters.month && filters.year) {
      filtered = filtered.filter(p => p.month === filters.month && p.year === filters.year);
    }

    // Filter by status
    if (filters.status) {
      filtered = filtered.filter(p => p.status === filters.status);
    }

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.fileName.toLowerCase().includes(searchLower) ||
        p.employeeName?.toLowerCase().includes(searchLower) ||
        p.clientName.toLowerCase().includes(searchLower)
      );
    }

    setFilteredPayslips(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [payslips, filters]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'processed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processed': return <CheckCircleIcon className="h-4 w-4" />;
      case 'processing': return <ArrowPathIcon className="h-4 w-4 animate-spin" />;
      case 'pending': return <ClockIcon className="h-4 w-4" />;
      case 'failed': return <XCircleIcon className="h-4 w-4" />;
      default: return <ExclamationTriangleIcon className="h-4 w-4" />;
    }
  };

  // Handle payslip download
  const handleDownload = async (payslip: PayslipFile) => {
    try {
      const response = await fetch(payslip.fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = payslip.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  // Handle payslip view
  const handleView = (payslip: PayslipFile) => {
    setSelectedPayslip(payslip);
    setShowDetailModal(true);
    setPreviewError(null);
    setPreviewLoading(true);
  };

  // Determine file type for preview
  const getFileType = (fileName: string, mimeType: string): 'pdf' | 'image' | 'unsupported' => {
    const extension = fileName.toLowerCase().split('.').pop();

    // Check by MIME type first
    if (mimeType.startsWith('image/')) {
      return 'image';
    }
    if (mimeType === 'application/pdf') {
      return 'pdf';
    }

    // Fallback to extension
    if (extension) {
      if (['pdf'].includes(extension)) {
        return 'pdf';
      }
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
        return 'image';
      }
    }

    return 'unsupported';
  };

  // Handle preview load success
  const handlePreviewLoad = () => {
    setPreviewLoading(false);
    setPreviewError(null);
  };

  // Handle preview load error
  const handlePreviewError = () => {
    setPreviewLoading(false);
    setPreviewError('Unable to preview this document. You can still download it to view the content.');
  };

  // Render document preview based on file type
  const renderDocumentPreview = (payslip: PayslipFile) => {
    const fileType = getFileType(payslip.fileName, payslip.fileType);

    if (previewLoading) {
      return (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <ArrowPathIcon className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-2" />
            <p className="text-gray-600">Loading preview...</p>
          </div>
        </div>
      );
    }

    if (previewError) {
      return (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">{previewError}</p>
            <button
              onClick={() => handleDownload(payslip)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center mx-auto"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Download to View
            </button>
          </div>
        </div>
      );
    }

    switch (fileType) {
      case 'pdf':
        return (
          <div className="border rounded-lg overflow-hidden bg-gray-50">
            <iframe
              src={payslip.fileUrl}
              className="w-full h-96"
              title={`Preview of ${payslip.fileName}`}
              onLoad={handlePreviewLoad}
              onError={handlePreviewError}
            />
          </div>
        );

      case 'image':
        return (
          <div className="border rounded-lg overflow-hidden bg-gray-50">
            <img
              src={payslip.fileUrl}
              alt={`Preview of ${payslip.fileName}`}
              className="w-full h-auto max-h-96 object-contain"
              onLoad={handlePreviewLoad}
              onError={handlePreviewError}
            />
          </div>
        );

      case 'unsupported':
      default:
        return (
          <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg border">
            <div className="text-center">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Preview not available for this file type</p>
              <p className="text-sm text-gray-500 mb-4">File: {payslip.fileName}</p>
              <button
                onClick={() => handleDownload(payslip)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center mx-auto"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Download to View
              </button>
            </div>
          </div>
        );
    }
  };

  // Pagination calculations
  const totalPages = Math.ceil(filteredPayslips.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPayslips = filteredPayslips.slice(startIndex, endIndex);

  if (loading) {
    return (
      <div className="h-screen bg-gray-50 flex flex-col">
        <div className="bg-white shadow-sm border-b border-gray-200 z-10">
          <div className="flex">
            <div className="w-72 flex-shrink-0"></div>
            <div className="flex-1 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <DocumentTextIcon className="h-8 w-8 mr-3 text-blue-600" />
                    Payslips Dashboard
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Manage and view uploaded payslips
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-1 overflow-hidden">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <ArrowPathIcon className="h-12 w-12 text-blue-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading payslips...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 z-10">
        <div className="flex">
          <div className="w-72 flex-shrink-0"></div>
          <div className="flex-1 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <DocumentTextIcon className="h-8 w-8 mr-3 text-blue-600" />
                  Payslips Dashboard
                </h1>
                <p className="text-gray-600 mt-1">
                  Manage and view uploaded payslips
                </p>
              </div>
              <div className="flex items-center space-x-4">
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <div className="flex-1 overflow-auto p-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Payslips</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.totalPayslips}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-lg">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Processed</p>
                  <p className="text-3xl font-bold text-green-600">{stats.processedCount}</p>
                </div>
                <div className="bg-green-100 p-3 rounded-lg">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>
          </div>

          {/* What's Next? Action Panel */}
          <div className="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">What's Next?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Scan Payslip */}
              <button
                onClick={() => {
                  router.push('/Pay/ScanPaySlip');
                }}
                className="group relative bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-200 rounded-xl p-6 text-left transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="Scan new payslip documents"
              >
                <div className="flex items-center mb-4">
                  <div className="bg-blue-600 p-3 rounded-lg group-hover:bg-blue-700 transition-colors duration-200">
                    <DocumentTextIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Scan Payslip</h3>
                <p className="text-sm text-gray-600">
                  Upload and process new payslip documents with AI-powered extraction
                </p>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <ArrowPathIcon className="h-5 w-5 text-blue-600 transform rotate-45" />
                </div>
              </button>

              {/* Scan Payslip Journal */}
              <button
                onClick={() => {
                  // TODO: Navigate to scan payslip journal page
                  console.log('Navigate to Scan Payslip Journal');
                }}
                className="group relative bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border border-green-200 rounded-xl p-6 text-left transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                aria-label="Process payslip journal entries"
              >
                <div className="flex items-center mb-4">
                  <div className="bg-green-600 p-3 rounded-lg group-hover:bg-green-700 transition-colors duration-200">
                    <BookOpenIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Scan Payslip Journal</h3>
                <p className="text-sm text-gray-600">
                  Process and analyze payslip journal entries for accounting integration
                </p>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <ArrowPathIcon className="h-5 w-5 text-green-600 transform rotate-45" />
                </div>
              </button>

              {/* Create New Payslip */}
              <button
                onClick={() => {
                                    router.push('/Pay/CreatePaySlip');
                  console.log('Navigate to Create New Payslip');
                }}
                className="group relative bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border border-purple-200 rounded-xl p-6 text-left transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                aria-label="Create new payslip record manually"
              >
                <div className="flex items-center mb-4">
                  <div className="bg-purple-600 p-3 rounded-lg group-hover:bg-purple-700 transition-colors duration-200">
                    <PlusCircleIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Create New Payslip</h3>
                <p className="text-sm text-gray-600">
                  Manually create and configure new payslip records from scratch
                </p>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <ArrowPathIcon className="h-5 w-5 text-purple-600 transform rotate-45" />
                </div>
              </button>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                {/* Client Filter */}
                <div className="min-w-0 flex-1 sm:max-w-xs">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Client</label>
                  <select
                    value={filters.clientId}
                    onChange={(e) => setFilters(prev => ({ ...prev, clientId: e.target.value }))}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="">All Clients</option>
                    {clients.map(client => (
                      <option key={client.id} value={client.id}>{client.name}</option>
                    ))}
                  </select>
                </div>

                {/* Month Filter */}
                <div className="min-w-0 flex-1 sm:max-w-xs">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Month</label>
                  <select
                    value={filters.month}
                    onChange={(e) => setFilters(prev => ({ ...prev, month: parseInt(e.target.value) }))}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value={0}>All Months</option>
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'long' })}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Year Filter */}
                <div className="min-w-0 flex-1 sm:max-w-xs">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
                  <select
                    value={filters.year}
                    onChange={(e) => setFilters(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - i;
                      return (
                        <option key={year} value={year}>{year}</option>
                      );
                    })}
                  </select>
                </div>

                {/* Status Filter */}
                <div className="min-w-0 flex-1 sm:max-w-xs">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="">All Status</option>
                    <option value="processed">Processed</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="failed">Failed</option>
                  </select>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                {/* Search */}
                <div className="min-w-0 flex-1 sm:max-w-md">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search"
                      value={filters.searchTerm}
                      onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                      className="w-full pl-10 pr-4 py-2 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>


              </div>
            </div>
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between mb-6">
            <p className="text-sm text-gray-600">
              Showing {startIndex + 1}-{Math.min(endIndex, filteredPayslips.length)} of {filteredPayslips.length} payslips
            </p>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => fetchPayslipsFromStorage()}
                className="text-blue-600 hover:text-blue-700 flex items-center text-sm font-medium"
              >
                <ArrowPathIcon className="h-4 w-4 mr-1" />
                Refresh
              </button>
            </div>
          </div>

          {/* Empty State */}
          {filteredPayslips.length === 0 ? (
            <div className="bg-white rounded-xl shadow-sm p-12 text-center border border-gray-100">
              <DocumentTextIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No payslips found</h3>
              <p className="text-gray-600 mb-6">
                {payslips.length === 0
                  ? "No payslips have been uploaded yet. Start by scanning some payslips."
                  : "No payslips match your current filters. Try adjusting your search criteria."
                }
              </p>
              <button
                onClick={() => router.push('/Pay/ScanPaySlip')}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center mx-auto"
              >
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Scan Payslips
              </button>
            </div>
          ) : (
            <>
              {/* Payslips List */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employee
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Client
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Period
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Upload Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Size
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {currentPayslips.map((payslip) => (
                        <tr key={payslip.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <DocumentTextIcon className="h-8 w-8 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {payslip.employeeName || 'Unknown Employee'}
                                </div>
                                <div className="text-sm text-gray-500 truncate max-w-xs">
                                  {payslip.fileName}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {payslip.clientName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {payslip.month}/{payslip.year}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payslip.status)}`}>
                              {getStatusIcon(payslip.status)}
                              <span className="ml-1 capitalize">{payslip.status}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {payslip.uploadDate.toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatFileSize(payslip.fileSize)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <button
                                onClick={() => handleView(payslip)}
                                className="text-blue-600 hover:text-blue-700"
                                title="View"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDownload(payslip)}
                                className="text-gray-600 hover:text-gray-700"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeftIcon className="h-4 w-4" />
                    </button>
                    <span className="text-sm text-gray-600">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRightIcon className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="text-sm text-gray-600">
                    {filteredPayslips.length} total payslips
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Detail Modal */}
      <Transition appear show={showDetailModal} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => {
          setShowDetailModal(false);
          setPreviewLoading(false);
          setPreviewError(null);
        }}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  {selectedPayslip && (
                    <>
                      <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 mb-4">
                        Payslip Details
                      </Dialog.Title>

                      <div className="space-y-4">
                        {/* Basic Info */}
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Employee</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {selectedPayslip.employeeName || 'Unknown Employee'}
                            </p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Client</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedPayslip.clientName}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Period</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {selectedPayslip.month}/{selectedPayslip.year}
                            </p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedPayslip.status)}`}>
                              {getStatusIcon(selectedPayslip.status)}
                              <span className="ml-1 capitalize">{selectedPayslip.status}</span>
                            </span>
                          </div>
                        </div>

                        {/* Document Preview */}
                        <div className="border-t pt-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-4">Document Preview</h4>
                          {renderDocumentPreview(selectedPayslip)}
                        </div>
                      </div>

                      <div className="mt-6 flex items-center justify-end space-x-3">
                        <button
                          type="button"
                          className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                          onClick={() => {
                            setShowDetailModal(false);
                            setPreviewLoading(false);
                            setPreviewError(null);
                          }}
                        >
                          Close
                        </button>
                        <button
                          type="button"
                          className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                          onClick={() => {
                            handleDownload(selectedPayslip);
                            setShowDetailModal(false);
                          }}
                        >
                          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                          Download
                        </button>
                      </div>
                    </>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default PayslipsDashboard;