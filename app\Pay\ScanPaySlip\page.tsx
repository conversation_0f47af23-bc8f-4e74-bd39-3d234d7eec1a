"use client";
import React, { useState, useCallback, useEffect, Fragment } from "react";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { useRouter } from "next/navigation";
import Sidebar from "../../DashBoard/SideBar";
import { fetchUserClients } from "../../../utils/accountUtils";
import {
  CloudArrowUpIcon,
  DocumentDuplicateIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  TableCellsIcon,
  CheckCircleIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";
import { GoogleGenAI } from "@google/genai";
import { useDropzone } from "react-dropzone";
import * as XLSX from 'xlsx';
import { Transition, Dialog } from '@headlessui/react';
import { storage, ref, uploadBytes, getDownloadURL } from "../../../firebase";
import { getEffectiveUserId } from "../../../utils/accountUtils";

// API Keys
const API_KEY3 = "AIzaSyA1b9WCqkH5KHkc5SCAS_3XAC7XtB97QoM"; // FREE TRIAL API KEY

// Interfaces
interface Employee {
  id?: string;
  firstName: string;
  lastName: string;
  position: string;
  department: string;
  employeeNumber: string;
  cnssNumber: string;
  bankAccount: string;
  address: string;
  phoneNumber: string;
  email: string;
  hireDate: string;
  baseSalary: number;
  isHeadOfHousehold: boolean;
  children: Child[];
}

interface Child {
  name: string;
  birthDate: string;
  isStudent: boolean;
  isDisabled: boolean;
}

interface PayslipData {
  employee: Employee;
  grossSalary: number;
  cnssContribution: number;
  taxableIncome: number;
  irpp: number;
  netDue: number;
  advances: number;
  netToPay: number;
  cnssEmployer: number;
  tfp: number;
  proplous: number;
  payPeriod: {
    month: number;
    year: number;
  };
}

interface PayrollJournalEntry {
  employeeName: string;
  grossSalary: number;
  cnssContribution: number;
  taxableIncome: number;
  irpp: number;
  netDue: number;
  advances: number;
  netToPay: number;
  cnssEmployer: number;
  tfp: number;
  proplous: number;
}

interface PayrollJournal {
  month: string;
  employer: string;
  entries: PayrollJournalEntry[];
  totals: PayrollJournalEntry;
}

interface FileUpload {
  file: File;
  id: string;
  status: 'uploading' | 'storage-uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  storageProgress: number;
  result?: PayslipData;
  error?: string;
  storageUrl?: string;
  storagePath?: string;
  storageError?: string;
}

interface Client {
  id: string;
  name: string;
  employeesNumber?: string;
  [key: string]: any;
}

const PayslipScanner: React.FC = () => {
  // State management
  const [user, setUser] = useState<any>(null);
  const [clients, setClients] = useState<Record<string, Client>>({});
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [files, setFiles] = useState<FileUpload[]>([]);
  const [payrollJournal, setPayrollJournal] = useState<PayrollJournal | null>(null);
  const [showJournalModal, setShowJournalModal] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [employerName, setEmployerName] = useState<string>("");
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationData, setValidationData] = useState<{
    uploadedCount: number;
    expectedCount: number;
    clientName: string;
  } | null>(null);
  const [pendingFiles, setPendingFiles] = useState<FileUpload[]>([]);

  const router = useRouter();

  // Authentication effect
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setUser(user);
        try {
          const clientsData = await fetchUserClients(user);
          console.log('Fetched clients:', clientsData);
          setClients(clientsData);
        } catch (error) {
          console.error('Error fetching clients:', error);
        }
      } else {
        router.push('/SignInScreen');
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Client selection effect
  useEffect(() => {
    if (selectedClientId && clients[selectedClientId]) {
      setSelectedClient(clients[selectedClientId]);
      setEmployerName(clients[selectedClientId].name || "");
    }
  }, [selectedClientId, clients]);

  // Payslip processing prompt - comprehensive AI agent for payroll journal generation
  const generatePayslipPrompt = () => {
    return `# AI Agent Prompt: Payslip Journal Generator

## Task Overview
You are an AI agent specialized in processing payslip data and creating structured payroll journals. Your task is to analyze individual payslips and consolidate them into a comprehensive monthly payroll journal following a standardized format.

## Input Format
You will receive a set of individual payslips containing employee salary information. Each payslip may include:
- Employee name
- Gross salary
- Various deductions (social security, taxes, etc.)
- Net salary
- Employer contributions
- Pay period information

## Output Requirements
Create a **PAYROLL JOURNAL** with the following structure:

### Header Information
- **PAYROLL JOURNAL** (title)
- **MONTH**: [Month in MM/YYYY format]
- **EMPLOYER**: [Employer name]

### Column Structure
Create a table with these columns:
1. **Employee Name**
2. **GROSS SALARIES**
3. **CNSS CONTRIBUTION** (Social Security Contributions)
4. **TAXABLE SALARIES**
5. **IRPP** (Income Tax)
6. **NET DUE**
7. **ADV./OPP.** (Advances/Oppositions)
8. **NET TO PAY**
9. **CNSS EMPLOYER** (Employer Social Security)
10. **TFP** (Professional Training Tax)
11. **PROPLOUS** (Additional Contributions)

### Processing Instructions

#### Data Extraction
1. Extract employee names from each payslip
2. Identify gross salary amounts
3. Calculate or extract all deduction types
4. Determine net amounts
5. Extract employer contribution amounts

#### Calculations
- Ensure mathematical consistency across all columns
- Verify that: Gross Salary - Deductions = Net Due
- Account for advances or other adjustments
- Calculate total employer contributions

#### Formatting
- Use consistent number formatting (e.g., 752,580 for large numbers)
- Align numbers properly in columns
- Include a "Grand Total" row at the bottom
- Sum all numerical columns accurately

#### Quality Checks
- Verify all employees from the payslips are included
- Cross-check calculations for accuracy
- Ensure no data is missing or duplicated
- Validate that totals match the sum of individual entries

## Output Format
Return the data as a JSON object with the following structure:
{
  "month": "MM/YYYY",
  "employer": "Company Name",
  "entries": [
    {
      "employeeName": "Employee Name",
      "grossSalary": 0,
      "cnssContribution": 0,
      "taxableIncome": 0,
      "irpp": 0,
      "netDue": 0,
      "advances": 0,
      "netToPay": 0,
      "cnssEmployer": 0,
      "tfp": 0,
      "proplous": 0
    }
  ],
  "totals": {
    "employeeName": "Grand Total",
    "grossSalary": 0,
    "cnssContribution": 0,
    "taxableIncome": 0,
    "irpp": 0,
    "netDue": 0,
    "advances": 0,
    "netToPay": 0,
    "cnssEmployer": 0,
    "tfp": 0,
    "proplous": 0
  }
}

## Error Handling
- If payslip data is incomplete, clearly indicate missing information
- If calculations don't balance, highlight discrepancies
- Provide summary of any assumptions made during processing
- Request clarification for ambiguous data points

## Additional Requirements
- Maintain professional formatting consistent with accounting standards
- Handle multiple currencies if applicable
- Process different pay periods and normalize to monthly view
- Flag any inconsistencies or missing data for review
- Preserve audit trail information when possible`;
  };

  // Process payslip with Gemini AI
  const processPayslipWithGemini = async (file: File): Promise<PayslipData | null> => {
    try {
      const base64String = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      const response = await ai.models.generateContent({
        model: "gemini-2.5-pro-preview-03-25",
        contents: [
          {
            role: "user",
            parts: [
              { text: "Please analyze this payslip document and extract the payroll information according to the instructions." },
              {
                inlineData: {
                  mimeType: file.type,
                  data: base64String
                }
              }
            ]
          }
        ],
        config: {
          systemInstruction: generatePayslipPrompt(),
          temperature: 0.2,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 65536,
        }
      });

      const responseText = response.text || "";
      return extractPayslipData(responseText);
    } catch (error) {
      console.error('Gemini processing error:', error);
      return null;
    }
  };

  // Extract payslip data from AI response
  const extractPayslipData = (responseText: string): PayslipData | null => {
    try {
      // Try to find JSON in the response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error('No JSON found in response');
        return null;
      }

      const jsonData = JSON.parse(jsonMatch[0]);

      // Convert the AI response to our PayslipData format
      if (jsonData.entries && jsonData.entries.length > 0) {
        const firstEntry = jsonData.entries[0];
        return {
          employee: {
            firstName: firstEntry.employeeName?.split(' ')[0] || '',
            lastName: firstEntry.employeeName?.split(' ').slice(1).join(' ') || '',
            position: '',
            department: '',
            employeeNumber: '',
            cnssNumber: '',
            bankAccount: '',
            address: '',
            phoneNumber: '',
            email: '',
            hireDate: '',
            baseSalary: firstEntry.grossSalary || 0,
            isHeadOfHousehold: false,
            children: []
          },
          grossSalary: firstEntry.grossSalary || 0,
          cnssContribution: firstEntry.cnssContribution || 0,
          taxableIncome: firstEntry.taxableIncome || 0,
          irpp: firstEntry.irpp || 0,
          netDue: firstEntry.netDue || 0,
          advances: firstEntry.advances || 0,
          netToPay: firstEntry.netToPay || 0,
          cnssEmployer: firstEntry.cnssEmployer || 0,
          tfp: firstEntry.tfp || 0,
          proplous: firstEntry.proplous || 0,
          payPeriod: {
            month: selectedMonth,
            year: selectedYear
          }
        };
      }

      return null;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return null;
    }
  };

  // Generate payroll journal from processed payslips
  const generatePayrollJournal = (payslips: PayslipData[]): PayrollJournal => {
    const entries: PayrollJournalEntry[] = payslips.map(payslip => ({
      employeeName: `${payslip.employee.firstName} ${payslip.employee.lastName}`,
      grossSalary: payslip.grossSalary,
      cnssContribution: payslip.cnssContribution,
      taxableIncome: payslip.taxableIncome,
      irpp: payslip.irpp,
      netDue: payslip.netDue,
      advances: payslip.advances,
      netToPay: payslip.netToPay,
      cnssEmployer: payslip.cnssEmployer,
      tfp: payslip.tfp,
      proplous: payslip.proplous
    }));

    // Calculate totals
    const totals: PayrollJournalEntry = {
      employeeName: "Grand Total",
      grossSalary: entries.reduce((sum, entry) => sum + entry.grossSalary, 0),
      cnssContribution: entries.reduce((sum, entry) => sum + entry.cnssContribution, 0),
      taxableIncome: entries.reduce((sum, entry) => sum + entry.taxableIncome, 0),
      irpp: entries.reduce((sum, entry) => sum + entry.irpp, 0),
      netDue: entries.reduce((sum, entry) => sum + entry.netDue, 0),
      advances: entries.reduce((sum, entry) => sum + entry.advances, 0),
      netToPay: entries.reduce((sum, entry) => sum + entry.netToPay, 0),
      cnssEmployer: entries.reduce((sum, entry) => sum + entry.cnssEmployer, 0),
      tfp: entries.reduce((sum, entry) => sum + entry.tfp, 0),
      proplous: entries.reduce((sum, entry) => sum + entry.proplous, 0)
    };

    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    return {
      month: `${monthNames[selectedMonth - 1]} ${selectedYear}`,
      employer: employerName,
      entries,
      totals
    };
  };

  // Export to Excel
  const exportToExcel = (journal: PayrollJournal) => {
    const excelData = [
      // Header rows
      ['PAYROLL JOURNAL'],
      [''],
      [`MONTH: ${journal.month}`],
      [`EMPLOYER: ${journal.employer}`],
      [''],
      // Column headers
      [
        'Employee Name',
        'GROSS SALARIES',
        'CNSS CONTRIBUTION',
        'TAXABLE SALARIES',
        'IRPP',
        'NET DUE',
        'ADV./OPP.',
        'NET TO PAY',
        'CNSS EMPLOYER',
        'TFP',
        'PROPLOUS'
      ],
      // Data rows
      ...journal.entries.map(entry => [
        entry.employeeName,
        entry.grossSalary,
        entry.cnssContribution,
        entry.taxableIncome,
        entry.irpp,
        entry.netDue,
        entry.advances,
        entry.netToPay,
        entry.cnssEmployer,
        entry.tfp,
        entry.proplous
      ]),
      // Totals row
      [
        journal.totals.employeeName,
        journal.totals.grossSalary,
        journal.totals.cnssContribution,
        journal.totals.taxableIncome,
        journal.totals.irpp,
        journal.totals.netDue,
        journal.totals.advances,
        journal.totals.netToPay,
        journal.totals.cnssEmployer,
        journal.totals.tfp,
        journal.totals.proplous
      ]
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Payroll Journal');

    const fileName = `Payroll_Journal_${journal.month.replace(' ', '_')}_${journal.employer.replace(/\s+/g, '_')}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  };

  // File upload handlers
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: FileUpload[] = acceptedFiles.map(file => ({
      file,
      id: Math.random().toString(36).substring(2, 11),
      status: 'uploading',
      progress: 0,
      storageProgress: 0
    }));

    // Validate payslip count before processing
    if (validatePayslipCount(newFiles)) {
      // If validation passes, add files and process them
      setFiles(prev => [...prev, ...newFiles]);
      newFiles.forEach(fileUpload => {
        processFile(fileUpload);
      });
    }
    // If validation fails, the modal will be shown and files will be held in pendingFiles
  }, [files.length, selectedClient]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg']
    },
    multiple: true
  });

  // Process individual file
  const processFile = async (fileUpload: FileUpload) => {
    try {
      // Step 1: Upload to Firebase Storage first
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, status: 'storage-uploading', progress: 0 }
          : f
      ));

      const storageResult = await uploadToFirebaseStorage(fileUpload);

      if (storageResult) {
        // Update with storage success
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? {
                ...f,
                storageUrl: storageResult.url,
                storagePath: storageResult.path,
                status: 'processing',
                progress: 25
              }
            : f
        ));
      } else {
        // Storage failed, but continue with AI processing (with warning)
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? {
                ...f,
                status: 'processing',
                progress: 25,
                storageError: 'Storage upload failed, but continuing with processing'
              }
            : f
        ));
      }

      // Step 2: Process with Gemini AI
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, progress: 50 }
          : f
      ));

      const result = await processPayslipWithGemini(fileUpload.file);

      if (result) {
        // Update with AI processing result
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? { ...f, status: 'completed', progress: 100, result }
            : f
        ));
      } else {
        // Update with AI processing error
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? { ...f, status: 'error', progress: 0, error: 'Failed to process payslip with AI' }
            : f
        ));
      }
    } catch (error) {
      console.error('Error processing file:', error);
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, status: 'error', progress: 0, error: 'Processing failed' }
          : f
      ));
    }
  };

  // Remove file
  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // Generate journal from all completed files
  const handleGenerateJournal = () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.result);
    if (completedFiles.length === 0) {
      alert('No processed payslips available to generate journal');
      return;
    }

    const payslips = completedFiles.map(f => f.result!);
    const journal = generatePayrollJournal(payslips);
    setPayrollJournal(journal);
    setShowJournalModal(true);
  };

  // Handle export
  const handleExport = () => {
    if (payrollJournal) {
      exportToExcel(payrollJournal);
    }
  };

  // Format number for display
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  // Validate payslip count against expected employee count
  const validatePayslipCount = (newFiles: FileUpload[]): boolean => {
    if (!selectedClient || !selectedClient.employeesNumber) {
      // If no client selected or no employee count available, allow upload
      return true;
    }

    const expectedCount = parseInt(selectedClient.employeesNumber, 10);
    if (isNaN(expectedCount) || expectedCount <= 0) {
      // If employee count is invalid, allow upload
      return true;
    }

    const totalUploadedCount = files.length + newFiles.length;

    if (totalUploadedCount < expectedCount) {
      // Show validation modal
      setValidationData({
        uploadedCount: totalUploadedCount,
        expectedCount: expectedCount,
        clientName: selectedClient.name
      });
      setPendingFiles(newFiles);
      setShowValidationModal(true);
      return false;
    }

    return true;
  };

  // Handle validation modal actions
  const handleContinueAnyway = () => {
    setShowValidationModal(false);
    if (pendingFiles.length > 0) {
      // Add pending files to the main files list and process them
      setFiles(prev => [...prev, ...pendingFiles]);
      pendingFiles.forEach(fileUpload => {
        processFile(fileUpload);
      });
      setPendingFiles([]);
    }
    setValidationData(null);
  };

  const handleCancelUpload = () => {
    setShowValidationModal(false);
    setPendingFiles([]);
    setValidationData(null);
  };

  // Upload file to Firebase Storage
  const uploadToFirebaseStorage = async (fileUpload: FileUpload): Promise<{url: string, path: string} | null> => {
    try {
      if (!user || !selectedClientId) {
        throw new Error("User not authenticated or no client selected");
      }

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      // Create structured path: /payslips/{clientId}/{year}/{month}/
      const year = selectedYear.toString();
      const month = selectedMonth.toString().padStart(2, '0');

      // Generate unique filename to prevent conflicts
      const timestamp = new Date().getTime();
      const fileExtension = fileUpload.file.name.split('.').pop() || '';
      const sanitizedFileName = fileUpload.file.name
        .replace(/[^a-zA-Z0-9.-]/g, '_')
        .replace(/_+/g, '_');

      const fileName = `payslips/${selectedClientId}/${year}/${month}/${timestamp}_${sanitizedFileName}`;

      // Create storage reference
      const storageRef = ref(storage, fileName);

      // Simulate progress updates during upload
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? { ...f, storageProgress: Math.min(f.storageProgress + 10, 90) }
            : f
        ));
      }, 200);

      // Upload file to Firebase Storage
      const snapshot = await uploadBytes(storageRef, fileUpload.file);
      clearInterval(progressInterval);

      // Update progress to 100%
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, storageProgress: 100 }
          : f
      ));

      // Get download URL
      const downloadUrl = await getDownloadURL(snapshot.ref);

      return {
        url: downloadUrl,
        path: fileName
      };
    } catch (error) {
      console.error('Firebase Storage upload error:', error);
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, storageError: 'Storage upload failed' }
          : f
      ));
      return null;
    }
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header - Full Width */}
      <div className="bg-white shadow-sm border-b border-gray-200 z-10">
        <div className="flex">
          {/* Spacer for sidebar width */}
          <div className="w-72 flex-shrink-0"></div>
          
          {/* Header content */}
          <div className="flex-1 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <DocumentTextIcon className="h-8 w-8 mr-3 text-blue-600" />
                  Payslip Scanner
                </h1>
                <p className="text-gray-600 mt-1">
                  Automatic payslip processing with payroll journal generation
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area with Sidebar */}
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Main Content */}
          <div className="flex-1 overflow-auto p-6">
            {/* Client Selection */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6 p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <UserGroupIcon className="h-5 w-5 mr-2 text-blue-600" />
                Client Selection
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Client
                  </label>
                  <select
                    value={selectedClientId}
                    onChange={(e) => setSelectedClientId(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a client</option>
                    {Object.entries(clients).map(([id, client]) => (
                      <option key={id} value={id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Month
                  </label>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 12 }, (_, i) => {
                      const monthNames = [
                        'January', 'February', 'March', 'April', 'May', 'June',
                        'July', 'August', 'September', 'October', 'November', 'December'
                      ];
                      return (
                        <option key={i + 1} value={i + 1}>
                          {monthNames[i]}
                        </option>
                      );
                    })}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Year
                  </label>
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - 2 + i;
                      return (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>
            </div>

            {/* File Upload Area */}
            {selectedClientId && (
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                    <CloudArrowUpIcon className="h-5 w-5 mr-2 text-blue-600" />
                    Payslip Upload
                  </h2>
                  <p className="text-gray-600 mt-1">
                    Drag and drop your payslips (PDF, PNG, JPG) or click to select
                  </p>
                </div>

                <div className="p-6">
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      isDragActive
                        ? 'border-blue-400 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <input {...getInputProps()} />
                    <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    {isDragActive ? (
                      <p className="text-blue-600 font-medium">
                        Drop files here...
                      </p>
                    ) : (
                      <div>
                        <p className="text-gray-600 mb-2">
                          Drag and drop your payslips here, or{' '}
                          <span className="text-blue-600 font-medium cursor-pointer">
                            click to select
                          </span>
                        </p>
                        <p className="text-sm text-gray-500">
                          Supported formats: PDF, PNG, JPG (max 10MB per file)
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Files List */}
            {files.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6">
                <div className="p-6 border-b border-gray-200 flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                    <DocumentDuplicateIcon className="h-5 w-5 mr-2 text-blue-600" />
                    Processed Files ({files.length})
                  </h2>

                  {files.some(f => f.status === 'completed') && (
                    <button
                      onClick={handleGenerateJournal}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                    >
                      <TableCellsIcon className="h-4 w-4 mr-2" />
                      Generate Journal
                    </button>
                  )}
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    {files.map((file) => (
                      <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
                              <span className="font-medium text-gray-900">
                                {file.file.name}
                              </span>
                              <span className="ml-2 text-sm text-gray-500">
                                ({(file.file.size / 1024 / 1024).toFixed(2)} MB)
                              </span>
                            </div>

                            {/* Progress bars */}
                            {(file.status === 'storage-uploading' || file.status === 'processing') && (
                              <div className="mt-2 space-y-2">
                                {/* Storage upload progress */}
                                <div>
                                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>Storage Upload</span>
                                    <span>{file.storageProgress}%</span>
                                  </div>
                                  <div className="bg-gray-200 rounded-full h-2">
                                    <div
                                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                      style={{ width: `${file.storageProgress}%` }}
                                    />
                                  </div>
                                </div>

                                {/* AI processing progress */}
                                {file.status === 'processing' && (
                                  <div>
                                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                                      <span>AI Processing</span>
                                      <span>{file.progress}%</span>
                                    </div>
                                    <div className="bg-gray-200 rounded-full h-2">
                                      <div
                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${file.progress}%` }}
                                      />
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Status */}
                            <div className="mt-2 space-y-1">
                              {file.status === 'completed' && (
                                <div className="flex items-center text-green-600">
                                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                                  <span className="text-sm">Successfully processed</span>
                                </div>
                              )}
                              {file.status === 'error' && (
                                <div className="flex items-center text-red-600">
                                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                                  <span className="text-sm">{file.error}</span>
                                </div>
                              )}
                              {file.status === 'storage-uploading' && (
                                <div className="flex items-center text-green-600">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2" />
                                  <span className="text-sm">Uploading to storage...</span>
                                </div>
                              )}
                              {file.status === 'processing' && (
                                <div className="flex items-center text-blue-600">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2" />
                                  <span className="text-sm">Processing with AI...</span>
                                </div>
                              )}

                              {/* Storage status indicators */}
                              {file.storageUrl && (
                                <div className="flex items-center text-green-600">
                                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  <span className="text-xs">Stored in Firebase</span>
                                </div>
                              )}
                              {file.storageError && (
                                <div className="flex items-center text-amber-600">
                                  <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                                  <span className="text-xs">{file.storageError}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          <button
                            onClick={() => removeFile(file.id)}
                            className="ml-4 p-1 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Information Panel */}
            <div className="bg-blue-50 border border-blue-100 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                <InformationCircleIcon className="h-5 w-5 mr-2" />
                AI-Powered Payslip Scanner
              </h3>
              <div className="space-y-2 text-blue-700">
                <p>• Automatic payslip processing with AI</p>
                <p>• Secure cloud storage in Firebase</p>
                <p>• Employee count validation</p>
                <p>• Extraction of salary data and contributions</p>
                <p>• Automatic payroll journal generation</p>
                <p>• Excel export compliant with accounting standards</p>
                <p>• Support for PDF, PNG, JPG formats</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payroll Journal Modal */}
      <Transition appear show={showJournalModal} as={Fragment}>
        <Dialog as="div" className="relative z-[60]" onClose={() => setShowJournalModal(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 mb-4 flex items-center justify-between"
                  >
                    <span className="flex items-center">
                      <TableCellsIcon className="h-6 w-6 mr-2 text-blue-600" />
                      Payroll Journal
                    </span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={handleExport}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Export Excel
                      </button>
                      <button
                        onClick={() => setShowJournalModal(false)}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </Dialog.Title>

                  {payrollJournal && (
                    <div className="mt-4">
                      {/* Header */}
                      <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">
                          PAYROLL JOURNAL
                        </h2>
                        <p className="text-lg text-gray-700">
                          MONTH: {payrollJournal.month}
                        </p>
                        <p className="text-lg text-gray-700">
                          EMPLOYER: {payrollJournal.employer}
                        </p>
                      </div>

                      {/* Table */}
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 border border-gray-300">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Employee Name
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Gross Salaries
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                CNSS Contribution
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Taxable Salaries
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                IRPP
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Net Due
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Adv./Opp.
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Net to Pay
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                CNSS Employer
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                TFP
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Proplous
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {payrollJournal.entries.map((entry, index) => (
                              <tr key={index} className="hover:bg-gray-50">
                                <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-300">
                                  {entry.employeeName}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.grossSalary)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.cnssContribution)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.taxableIncome)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.irpp)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.netDue)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.advances)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.netToPay)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.cnssEmployer)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {formatNumber(entry.tfp)}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                  {formatNumber(entry.proplous)}
                                </td>
                              </tr>
                            ))}
                            {/* Totals row */}
                            <tr className="bg-gray-100 font-bold">
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r border-gray-300">
                                {payrollJournal.totals.employeeName}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.grossSalary)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.cnssContribution)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.taxableIncome)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.irpp)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.netDue)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.advances)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.netToPay)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.cnssEmployer)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right border-r border-gray-300">
                                {formatNumber(payrollJournal.totals.tfp)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                                {formatNumber(payrollJournal.totals.proplous)}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Payslip Validation Modal */}
      <Transition appear show={showValidationModal} as={Fragment}>
        <Dialog as="div" className="relative z-[70]" onClose={() => {}}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 mb-4 flex items-center"
                  >
                    <ExclamationTriangleIcon className="h-6 w-6 mr-2 text-amber-500" />
                    Payslip Count Validation
                  </Dialog.Title>

                  {validationData && (
                    <div className="mt-4">
                      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
                        <div className="flex items-start">
                          <ExclamationTriangleIcon className="h-5 w-5 text-amber-400 mt-0.5 mr-3 flex-shrink-0" />
                          <div>
                            <h4 className="text-sm font-medium text-amber-800 mb-2">
                              Potential Missing Payslips Detected
                            </h4>
                            <p className="text-sm text-amber-700">
                              You have uploaded <strong>{validationData.uploadedCount}</strong> payslip{validationData.uploadedCount !== 1 ? 's' : ''} but{' '}
                              <strong>{validationData.clientName}</strong> has{' '}
                              <strong>{validationData.expectedCount}</strong> employee{validationData.expectedCount !== 1 ? 's' : ''}.
                            </p>
                            <p className="text-sm text-amber-700 mt-2">
                              You may have missing payslips. Would you like to continue anyway or go back to add more files?
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={handleCancelUpload}
                          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                        >
                          Cancel / Go Back
                        </button>
                        <button
                          onClick={handleContinueAnyway}
                          className="px-4 py-2 text-sm font-medium text-white bg-amber-600 border border-transparent rounded-lg hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors"
                        >
                          Continue Anyway
                        </button>
                      </div>
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default PayslipScanner;