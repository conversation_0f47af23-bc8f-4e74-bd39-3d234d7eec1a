[debug] [2025-06-02T10:54:11.666Z] ----------------------------------------------------------------------
[debug] [2025-06-02T10:54:11.667Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-06-02T10:54:11.668Z] CLI Version:   13.35.1
[debug] [2025-06-02T10:54:11.668Z] Platform:      win32
[debug] [2025-06-02T10:54:11.668Z] Node Version:  v18.20.4
[debug] [2025-06-02T10:54:11.672Z] Time:          Mon Jun 02 2025 11:54:11 GMT+0100 (Central European Standard Time)
[debug] [2025-06-02T10:54:11.673Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-02T10:54:11.676Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-06-02T10:54:11.879Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-02T10:54:11.880Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-06-02T10:54:11.890Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T10:54:11.890Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-02T10:54:11.895Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-ggbinvoices.json
[debug] [2025-06-02T10:54:11.914Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T10:54:11.914Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T10:54:11.914Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T10:54:11.914Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-02T10:54:11.928Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\hamamerdessi00_gmail_com_application_default_credentials.json
[debug] [2025-06-02T10:54:11.929Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\hamamerdessi00_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\hamamerdessi00_gmail_com_application_default_credentials.json"}}
[debug] [2025-06-02T10:54:11.930Z] Checked if tokens are valid: false, expires at: 1745414058448
[debug] [2025-06-02T10:54:11.930Z] Checked if tokens are valid: false, expires at: 1745414058448
[debug] [2025-06-02T10:54:11.931Z] > refreshing access token with scopes: []
[debug] [2025-06-02T10:54:11.931Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-02T10:54:11.931Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-02T10:54:12.242Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-02T10:54:12.243Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-02T10:54:12.251Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/ggbinvoices/adminSdkConfig [none]
[debug] [2025-06-02T10:54:12.326Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-06-02T10:54:12.326Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-06-02T10:54:12.978Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/ggbinvoices/adminSdkConfig 200
[debug] [2025-06-02T10:54:12.978Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/ggbinvoices/adminSdkConfig {"projectId":"ggbinvoices","databaseURL":"https://ggbinvoices-default-rtdb.europe-west1.firebasedatabase.app","storageBucket":"ggbinvoices.appspot.com","locationId":"europe-west"}
[info] i  functions: Watching "D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\" for Cloud Functions..."}}
[debug] [2025-06-02T10:54:13.015Z] Validating nodejs source
[warn] !  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[debug] [2025-06-02T10:54:14.984Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "@google/generative-ai": "^0.21.0",
    "axios": "^1.7.9",
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "google-auth-library": "^9.15.0",
    "googleapis": "^144.0.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-06-02T10:54:14.985Z] Building nodejs source
[debug] [2025-06-02T10:54:14.985Z] Failed to find version of module node: reached end of search path D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules
[info] +  functions: Using node@18 from host. 
[debug] [2025-06-02T10:54:14.987Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-02T10:54:14.997Z] Found firebase-functions binary at 'D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8070

[debug] [2025-06-02T10:54:18.157Z] Got response from /__/functions.yaml {"endpoints":{"addInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addInvoiceToSheet"},"addAchatInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addAchatInvoiceToSheet"},"addTransactionToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addTransactionToSheet"},"addCaisseInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addCaisseInvoiceToSheet"},"getExchangeRate":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRate"},"getExchangeRateUsd":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRateUsd"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd."}}
[info] +  functions[us-central1-addInvoiceToSheet]: http function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addInvoiceToSheet). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addInvoiceToSheet)."}}
[info] +  functions[us-central1-addAchatInvoiceToSheet]: http function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addAchatInvoiceToSheet). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addAchatInvoiceToSheet)."}}
[info] +  functions[us-central1-addTransactionToSheet]: http function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addTransactionToSheet). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addTransactionToSheet)."}}
[info] +  functions[us-central1-addCaisseInvoiceToSheet]: http function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addCaisseInvoiceToSheet). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/addCaisseInvoiceToSheet)."}}
[info] +  functions[us-central1-getExchangeRate]: http function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/getExchangeRate). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/getExchangeRate)."}}
[info] +  functions[us-central1-getExchangeRateUsd]: http function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/getExchangeRateUsd). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/ggbinvoices/us-central1/getExchangeRateUsd)."}}
[debug] [2025-06-02T10:54:18.202Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: connect ECONNREFUSED ::1:40001. If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-06-02T10:54:37.182Z] [work-queue] {"queuedWork":["/ggbinvoices/us-central1/getExchangeRate-2025-06-02T10:54:37.182Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-06-02T10:54:37.182Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/ggbinvoices/us-central1/getExchangeRate-2025-06-02T10:54:37.182Z"],"workRunningCount":1}
[debug] [2025-06-02T10:54:37.182Z] Accepted request GET /ggbinvoices/us-central1/getExchangeRate?date=2025-05-14 --> us-central1-getExchangeRate
[debug] [2025-06-02T10:54:37.185Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-06-02T10:54:37.185Z] [functions] Got req.url=/ggbinvoices/us-central1/getExchangeRate?date=2025-05-14, mapping to path=/?date=2025-05-14 {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/ggbinvoices/us-central1/getExchangeRate?date=2025-05-14, mapping to path=/?date=2025-05-14"}}
[debug] [2025-06-02T10:54:37.196Z] [worker-pool] addWorker(us-central1-getExchangeRate) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(us-central1-getExchangeRate)"}}
[debug] [2025-06-02T10:54:37.197Z] [worker-pool] Adding worker with key us-central1-getExchangeRate, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key us-central1-getExchangeRate, total=1"}}
[debug] [2025-06-02T10:54:38.495Z] [runtime-status] [21024] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-06-02T10:54:38.496Z] [runtime-status] [21024] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:54:38.497Z] [runtime-status] [21024] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-06-02T10:54:38.498Z] [runtime-status] [21024] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:54:38.880Z] [runtime-status] [21024] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-06-02T10:54:38.880Z] [runtime-status] [21024] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-06-02T10:54:38.881Z] [runtime-status] [21024] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:54:38.891Z] [runtime-status] [21024] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-06-02T10:54:38.894Z] [runtime-status] [21024] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:54:38.894Z] [runtime-status] [21024] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[debug] [2025-06-02T10:54:40.370Z] [runtime-status] [21024] Functions runtime initialized. {"cwd":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions","node_version":"18.20.4"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Functions runtime initialized. {\"cwd\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\",\"node_version\":\"18.20.4\"}"}}
[debug] [2025-06-02T10:54:40.370Z] [runtime-status] [21024] Listening to port: \\?\pipe\fire_emu_6bbbafdf3b04d53c {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [21024] Listening to port: \\\\?\\pipe\\fire_emu_6bbbafdf3b04d53c"}}
[debug] [2025-06-02T10:54:40.458Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: IDLE"}}
[debug] [2025-06-02T10:54:40.459Z] [worker-pool] submitRequest(triggerId=us-central1-getExchangeRate) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-getExchangeRate)"}}
[info] i  functions: Beginning execution of "us-central1-getExchangeRate" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Beginning execution of \"us-central1-getExchangeRate\""}}
[debug] [2025-06-02T10:54:40.459Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: BUSY"}}
[debug] [2025-06-02T10:54:41.033Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-getExchangeRate" in 574.4054ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finished \"us-central1-getExchangeRate\" in 574.4054ms"}}
[debug] [2025-06-02T10:54:41.035Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: IDLE"}}
[debug] [2025-06-02T10:54:41.035Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-06-02T10:54:41.035Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-06-02T10:54:41.035Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-06-02T10:55:46.308Z] [work-queue] {"queuedWork":["/ggbinvoices/us-central1/getExchangeRate-2025-06-02T10:55:46.308Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-06-02T10:55:46.308Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/ggbinvoices/us-central1/getExchangeRate-2025-06-02T10:55:46.308Z"],"workRunningCount":1}
[debug] [2025-06-02T10:55:46.308Z] Accepted request GET /ggbinvoices/us-central1/getExchangeRate?date=2025-05-14 --> us-central1-getExchangeRate
[debug] [2025-06-02T10:55:46.311Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-06-02T10:55:46.311Z] [functions] Got req.url=/ggbinvoices/us-central1/getExchangeRate?date=2025-05-14, mapping to path=/?date=2025-05-14 {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/ggbinvoices/us-central1/getExchangeRate?date=2025-05-14, mapping to path=/?date=2025-05-14"}}
[debug] [2025-06-02T10:55:46.312Z] [worker-pool] submitRequest(triggerId=us-central1-getExchangeRate) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-getExchangeRate)"}}
[info] i  functions: Beginning execution of "us-central1-getExchangeRate" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Beginning execution of \"us-central1-getExchangeRate\""}}
[debug] [2025-06-02T10:55:46.312Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: BUSY"}}
[debug] [2025-06-02T10:55:46.773Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-getExchangeRate" in 461.738ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finished \"us-central1-getExchangeRate\" in 461.738ms"}}
[debug] [2025-06-02T10:55:46.774Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: IDLE"}}
[debug] [2025-06-02T10:55:46.774Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-06-02T10:55:46.774Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-06-02T10:55:46.774Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-06-02T10:56:46.771Z] File D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\FetchEuroRates.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\FetchEuroRates.js changed, reloading triggers"}}
[debug] [2025-06-02T10:56:47.776Z] Validating nodejs source
[warn] !  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[debug] [2025-06-02T10:56:48.726Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "@google/generative-ai": "^0.21.0",
    "axios": "^1.7.9",
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "google-auth-library": "^9.15.0",
    "googleapis": "^144.0.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-06-02T10:56:48.726Z] Building nodejs source
[debug] [2025-06-02T10:56:48.726Z] Failed to find version of module node: reached end of search path D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules
[info] +  functions: Using node@18 from host. 
[debug] [2025-06-02T10:56:48.727Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-02T10:56:48.733Z] Found firebase-functions binary at 'D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8896

[debug] [2025-06-02T10:56:51.251Z] Got response from /__/functions.yaml {"endpoints":{"addInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addInvoiceToSheet"},"addAchatInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addAchatInvoiceToSheet"},"addTransactionToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addTransactionToSheet"},"addCaisseInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addCaisseInvoiceToSheet"},"getExchangeRate":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRate"},"getExchangeRateUsd":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRateUsd"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd."}}
[debug] [2025-06-02T10:56:51.285Z] [worker-pool] Shutting down IDLE worker (us-central1-getExchangeRate) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Shutting down IDLE worker (us-central1-getExchangeRate)"}}
[debug] [2025-06-02T10:56:51.285Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: FINISHING {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: FINISHING"}}
[debug] [2025-06-02T10:56:51.297Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: exited {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: exited"}}
[debug] [2025-06-02T10:56:51.297Z] [worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: FINISHED {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-0860c803-dd6d-4abf-9c1a-081787c47428]: FINISHED"}}
[debug] [2025-06-02T10:57:39.960Z] File D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\FetchEuroRates.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\FetchEuroRates.js changed, reloading triggers"}}
[debug] [2025-06-02T10:57:40.976Z] Validating nodejs source
[warn] !  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[debug] [2025-06-02T10:57:42.238Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "@google/generative-ai": "^0.21.0",
    "axios": "^1.7.9",
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "google-auth-library": "^9.15.0",
    "googleapis": "^144.0.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-06-02T10:57:42.238Z] Building nodejs source
[debug] [2025-06-02T10:57:42.238Z] Failed to find version of module node: reached end of search path D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules
[info] +  functions: Using node@18 from host. 
[debug] [2025-06-02T10:57:42.239Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-02T10:57:42.246Z] Found firebase-functions binary at 'D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules\.bin\firebase-functions'
[debug] [2025-06-02T10:57:42.251Z] File D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\FetchEuroRates.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\FetchEuroRates.js changed, reloading triggers"}}
[info] Serving at port 8972

[debug] [2025-06-02T10:57:43.253Z] Validating nodejs source
[warn] !  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[debug] [2025-06-02T10:57:44.249Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "@google/generative-ai": "^0.21.0",
    "axios": "^1.7.9",
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "google-auth-library": "^9.15.0",
    "googleapis": "^144.0.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-06-02T10:57:44.249Z] Building nodejs source
[debug] [2025-06-02T10:57:44.249Z] Failed to find version of module node: reached end of search path D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules
[info] +  functions: Using node@18 from host. 
[debug] [2025-06-02T10:57:44.250Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-02T10:57:44.256Z] Found firebase-functions binary at 'D:\GeniusGB\GGB Projects\GeniusInvoiceProject\geniusinvoices\functions\node_modules\.bin\firebase-functions'
[debug] [2025-06-02T10:57:44.350Z] Got response from /__/functions.yaml {"endpoints":{"addInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addInvoiceToSheet"},"addAchatInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addAchatInvoiceToSheet"},"addTransactionToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addTransactionToSheet"},"addCaisseInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addCaisseInvoiceToSheet"},"getExchangeRate":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRate"},"getExchangeRateUsd":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRateUsd"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd."}}
[info] Serving at port 8267

[debug] [2025-06-02T10:57:46.284Z] Got response from /__/functions.yaml {"endpoints":{"addInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addInvoiceToSheet"},"addAchatInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addAchatInvoiceToSheet"},"addTransactionToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addTransactionToSheet"},"addCaisseInvoiceToSheet":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"addCaisseInvoiceToSheet"},"getExchangeRate":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRate"},"getExchangeRateUsd":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"getExchangeRateUsd"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: addInvoiceToSheet, addAchatInvoiceToSheet, addTransactionToSheet, addCaisseInvoiceToSheet, getExchangeRate, getExchangeRateUsd."}}
[debug] [2025-06-02T10:57:57.802Z] [work-queue] {"queuedWork":["/ggbinvoices/us-central1/getExchangeRate-2025-06-02T10:57:57.802Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-06-02T10:57:57.802Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/ggbinvoices/us-central1/getExchangeRate-2025-06-02T10:57:57.802Z"],"workRunningCount":1}
[debug] [2025-06-02T10:57:57.802Z] Accepted request GET /ggbinvoices/us-central1/getExchangeRate?date=2025-05-14 --> us-central1-getExchangeRate
[debug] [2025-06-02T10:57:57.805Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-06-02T10:57:57.805Z] [functions] Got req.url=/ggbinvoices/us-central1/getExchangeRate?date=2025-05-14, mapping to path=/?date=2025-05-14 {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/ggbinvoices/us-central1/getExchangeRate?date=2025-05-14, mapping to path=/?date=2025-05-14"}}
[debug] [2025-06-02T10:57:57.805Z] [worker-pool] Cleaned up workers for us-central1-getExchangeRate: 1 --> 0 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Cleaned up workers for us-central1-getExchangeRate: 1 --> 0"}}
[debug] [2025-06-02T10:57:57.810Z] [worker-pool] addWorker(us-central1-getExchangeRate) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(us-central1-getExchangeRate)"}}
[debug] [2025-06-02T10:57:57.810Z] [worker-pool] Adding worker with key us-central1-getExchangeRate, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key us-central1-getExchangeRate, total=1"}}
[debug] [2025-06-02T10:57:59.015Z] [runtime-status] [12152] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-06-02T10:57:59.017Z] [runtime-status] [12152] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:57:59.018Z] [runtime-status] [12152] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-06-02T10:57:59.019Z] [runtime-status] [12152] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:57:59.374Z] [runtime-status] [12152] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-06-02T10:57:59.375Z] [runtime-status] [12152] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-06-02T10:57:59.376Z] [runtime-status] [12152] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:57:59.380Z] [runtime-status] [12152] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-06-02T10:57:59.383Z] [runtime-status] [12152] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.1.2","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.1.2\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-06-02T10:57:59.383Z] [runtime-status] [12152] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[debug] [2025-06-02T10:58:00.756Z] [runtime-status] [12152] Functions runtime initialized. {"cwd":"D:\\GeniusGB\\GGB Projects\\GeniusInvoiceProject\\geniusinvoices\\functions","node_version":"18.20.4"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Functions runtime initialized. {\"cwd\":\"D:\\\\GeniusGB\\\\GGB Projects\\\\GeniusInvoiceProject\\\\geniusinvoices\\\\functions\",\"node_version\":\"18.20.4\"}"}}
[debug] [2025-06-02T10:58:00.757Z] [runtime-status] [12152] Listening to port: \\?\pipe\fire_emu_9001bf3bc42498d6 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[runtime-status] [12152] Listening to port: \\\\?\\pipe\\fire_emu_9001bf3bc42498d6"}}
[debug] [2025-06-02T10:58:00.835Z] [worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: IDLE"}}
[debug] [2025-06-02T10:58:00.835Z] [worker-pool] submitRequest(triggerId=us-central1-getExchangeRate) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-getExchangeRate)"}}
[info] i  functions: Beginning execution of "us-central1-getExchangeRate" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Beginning execution of \"us-central1-getExchangeRate\""}}
[debug] [2025-06-02T10:58:00.836Z] [worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: BUSY"}}
[debug] [2025-06-02T10:58:01.101Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-getExchangeRate" in 266.6395ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finished \"us-central1-getExchangeRate\" in 266.6395ms"}}
[debug] [2025-06-02T10:58:01.103Z] [worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: IDLE"}}
[debug] [2025-06-02T10:58:01.103Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-06-02T10:58:01.103Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-06-02T10:58:01.103Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-06-02T11:13:57.242Z] [worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: exited {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: exited"}}
[debug] [2025-06-02T11:13:57.242Z] [worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: FINISHED {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-getExchangeRate"},"extension":{},"message":"[worker-us-central1-getExchangeRate-efbd845c-587a-49d5-a5bd-3e33f201f573]: FINISHED"}}
[debug] [2025-06-02T11:13:57.243Z] Received signal SIGHUP 1
[info]  
[info] i  emulators: Received SIGHUP for the first time. Starting a clean shutdown. 
[info] i  emulators: Please wait for a clean shutdown or send the SIGHUP signal again to stop right now. 
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  ui: Stopping Emulator UI {"metadata":{"emulator":{"name":"ui"},"message":"Stopping Emulator UI"}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  eventarc: Stopping Eventarc Emulator {"metadata":{"emulator":{"name":"eventarc"},"message":"Stopping Eventarc Emulator"}}
[info] i  tasks: Stopping Cloud Tasks Emulator {"metadata":{"emulator":{"name":"tasks"},"message":"Stopping Cloud Tasks Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[info] i  logging: Stopping Logging Emulator {"metadata":{"emulator":{"name":"logging"},"message":"Stopping Logging Emulator"}}
[debug] [2025-06-02T11:13:57.254Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: connect ECONNREFUSED ::1:40001. If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
